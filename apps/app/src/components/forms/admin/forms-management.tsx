"use client"

import React, { useState } from "react"
import { Archive, FileText } from "lucide-react"

import { trpc } from "@/lib/trpc/client"
import { Tab, Tabs } from "@nextui-org/tabs"

import { ArchivedFormsTable } from "./archived-forms-table"
import { FormsTable } from "./forms-table"



interface FormsManagementProps {
  initialActivePagination: {
    page: number
    pageSize: number
    total: number
    totalPages: number
  }
  initialArchivedPagination: {
    page: number
    pageSize: number
    total: number
    totalPages: number
  }
}

export const FormsManagement: React.FC<FormsManagementProps> = ({
  initialActivePagination,
  initialArchivedPagination,
}) => {
  const [selectedTab, setSelectedTab] = useState("active")
  const [reloadAllTab, setReloadAllTab] = useState(false);
  const [reloadArchivedTab, setReloadArchivedTab] = useState(false);

  const utils = trpc.useUtils()

  const { onArchive, onUnarchive } = {
    onArchive: () => {
      setReloadArchivedTab(true)
      // Invalidate count queries to ensure fresh data
      utils.form.countByStatus.invalidate()
    },
    onUnarchive: () => {
      setReloadAllTab(true)
      // Invalidate count queries to ensure fresh data
      utils.form.countByStatus.invalidate()
    },
  }

  const { data: total } = trpc.form.countByStatus.useQuery({})

  const { data: archivedTotal } = trpc.form.countByStatus.useQuery({ status: "ARCHIVED" })

  return (
    <div className="space-y-6">
      <Tabs
        selectedKey={selectedTab}
        onSelectionChange={(key) => setSelectedTab(key as string)}
        aria-label="Gestion des formulaires"
        color="primary"
        variant="underlined"
        classNames={{
          tabList: "gap-6 w-full relative rounded-none p-0 border-b border-divider",
          cursor: "w-full bg-primary",
          tab: "max-w-fit px-0 h-12",
          tabContent: "group-data-[selected=true]:text-primary"
        }}
      >
        <Tab
          key="active"
          title={
            <div className="flex items-center space-x-2">
              <FileText className="size-4" />
              <span>Formulaires actifs</span>
              {total !== undefined && archivedTotal !== undefined &&
                <span className="bg-primary-100 text-primary-600 text-xs px-2 py-1 rounded-full">
                  {Math.max(0, total - archivedTotal)}
                </span>}
            </div>
          }
        >
          <div className="mt-6">
            <FormsTable
              initialPagination={initialActivePagination}
              reload={reloadAllTab}
              setReload={setReloadAllTab}
              onArchive={onArchive}
            />
          </div>
        </Tab>

        <Tab
          key="archived"
          title={
            <div className="flex items-center space-x-2">
              <Archive className="size-4" />
              <span>Formulaires archivés</span>
              {archivedTotal !== undefined && archivedTotal > 0 && <span className="bg-warning-100 text-warning-600 text-xs px-2 py-1 rounded-full">
                {archivedTotal}
              </span>}
            </div>
          }
        >
          <div className="mt-6">
            <ArchivedFormsTable
              initialPagination={initialArchivedPagination}
              reload={reloadArchivedTab}
              setReload={setReloadArchivedTab}
              onUnarchive={onUnarchive}
            />
          </div>
        </Tab>
      </Tabs>
    </div>
  )
}
