"use client"

import React from "react"

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@nextui-org/modal"
import { QuestionType } from "@prisma/client"

import { DynamicForm } from "../dynamic-form"

interface FormPreviewModalProps {
  isOpen: boolean
  onClose: () => void
  form: {
    id: string
    title: string
    description?: string | null
    type: string
    questions: Array<{
      id: string
      title: string
      description?: string | null
      type: QuestionType
      order: number
      isRequired: boolean
      minLength?: number
      maxLength?: number
      minValue?: number
      maxValue?: number
      options?: string | Array<{ id: string; label: string; value: string }> | null
    }>
  } | null
}

export const FormPreviewModal: React.FC<FormPreviewModalProps> = ({
  isOpen,
  onClose,
  form,
}) => {
  if (!form) return null

  const getQuestionTypeLabel = (type: QuestionType) => {
    switch (type) {
      case QuestionType.TEXT_SHORT:
        return "Texte court"
      case QuestionType.TEXT_LONG:
        return "Texte long"
      case QuestionType.EMAIL:
        return "Email"
      case QuestionType.NUMBER:
        return "Nombre"
      case QuestionType.RATING:
        return "Note"
      case QuestionType.YES_NO:
        return "Oui/Non"
      case QuestionType.SINGLE_CHOICE:
        return "Choix unique"
      case QuestionType.MULTIPLE_CHOICE:
        return "Choix multiple"
      case QuestionType.DATE:
        return "Date"
      default:
        return type
    }
  }

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="full" scrollBehavior="inside">
      <ModalContent>
        <ModalHeader className="flex justify-between items-center">
          <div>
            <h3 className="text-xl font-semibold">Aperçu du formulaire</h3>
            <p className="text-sm text-default-500">{form.title}</p>
          </div>
        </ModalHeader>
        <ModalBody className="p-0">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 p-6">
            {/* Left side: Form structure */}
            <div className="space-y-4">
              <h4 className="font-semibold text-lg">Structure du formulaire</h4>

              <div className="space-y-3">
                <div>
                  <label className="text-sm font-medium text-default-700">Titre</label>
                  <p className="text-default-600">{form.title}</p>
                </div>

                {form.description && (
                  <div>
                    <label className="text-sm font-medium text-default-700">Description</label>
                    <p className="text-default-600">{form.description}</p>
                  </div>
                )}

                <div>
                  <label className="text-sm font-medium text-default-700">Type</label>
                  <p className="text-default-600">
                    {form.type === "SUBSCRIPTION_CANCELLATION"
                      ? "Annulation d'abonnement"
                      : form.type}
                  </p>
                </div>

                <div>
                  <label className="text-sm font-medium text-default-700">
                    Questions ({form.questions.length})
                  </label>
                  <div className="space-y-2 mt-2">
                    {form.questions
                      .sort((a, b) => a.order - b.order)
                      .map((question, index) => (
                        <div
                          key={question.id}
                          className="p-3 border border-default-200 rounded-lg space-y-2"
                        >
                          <div className="flex items-center justify-between">
                            <span className="text-sm font-medium">
                              Question {index + 1}
                              {question.isRequired && (
                                <span className="text-danger ml-1">*</span>
                              )}
                            </span>
                            <span className="text-xs bg-default-100 px-2 py-1 rounded">
                              {getQuestionTypeLabel(question.type)}
                            </span>
                          </div>

                          <p className="text-sm text-default-700">{question.title}</p>

                          {question.description && (
                            <p className="text-xs text-default-500">{question.description}</p>
                          )}

                          {/* Show validation rules */}
                          <div className="text-xs text-default-400 space-y-1">
                            {question.minLength && (
                              <div>Min: {question.minLength} caractères</div>
                            )}
                            {question.maxLength && (
                              <div>Max: {question.maxLength} caractères</div>
                            )}
                            {question.minValue !== undefined && (
                              <div>Min: {question.minValue}</div>
                            )}
                            {question.maxValue !== undefined && (
                              <div>Max: {question.maxValue}</div>
                            )}
                            {question.options && (
                              <div>
                                Options: {
                                  typeof question.options === 'string'
                                    ? (JSON.parse(question.options) as Array<{ id: string; label: string; value: string }>).length
                                    : question.options.length
                                } choix
                              </div>
                            )}
                          </div>
                        </div>
                      ))}
                  </div>
                </div>
              </div>
            </div>

            {/* Right side: Live preview */}
            <div className="space-y-4">
              <h4 className="font-semibold text-lg">Aperçu en direct</h4>

              <div className="border border-default-200 rounded-lg p-4 bg-default-50">
                <p className="text-sm text-default-500 mb-4">
                  Voici comment les utilisateurs verront ce formulaire :
                </p>

                <div className="rounded-lg">
                  <DynamicForm
                    formType="SUBSCRIPTION_CANCELLATION"
                    onSubmissionComplete={() => {
                      // Preview mode - don't actually submit
                    }}
                    onCancel={() => {
                      // Preview mode - don't actually cancel
                    }}
                    className="border-none shadow-none"
                  />
                </div>
              </div>

              <div className="text-xs text-default-400 space-y-1">
                <p>• Ce formulaire sera présenté lors de l&apos;annulation d&apos;abonnement</p>
                <p>• Les réponses seront sauvegardées et analysables</p>
                <p>• La validation se fait en temps réel</p>
              </div>
            </div>
          </div>
        </ModalBody>
      </ModalContent>
    </Modal>
  )
}
