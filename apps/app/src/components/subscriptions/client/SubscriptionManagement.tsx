"use client"

import React, { useEffect, useState } from "react"
import { format } from "date-fns"
import { fr } from "date-fns/locale"
import { AlertTriangle, Calendar, CreditCard, Package, X } from "lucide-react"
import { toast } from "react-toastify"

import { trpc } from "@/lib/trpc/client"
import { <PERSON><PERSON> } from "@nextui-org/button"
import { Card, CardBody, CardHeader } from "@nextui-org/card"
import { Chip } from "@nextui-org/chip"
import { Divider } from "@nextui-org/divider"
import { Modal, ModalBody, ModalContent, ModalFooter, ModalHeader } from "@nextui-org/modal"
import { Spinner } from "@nextui-org/spinner"
import { useDisclosure } from "@nextui-org/use-disclosure"

import { DynamicForm } from "../../forms/dynamic-form"

interface SubscriptionManagementProps {
  className?: string
}

const SubscriptionManagement: React.FC<SubscriptionManagementProps> = ({ className }) => {
  const [isLoading, setIsLoading] = useState(false)
  const [showForm, setShowForm] = useState(false)
  const [formSubmissionId, setFormSubmissionId] = useState<string | null>(null)
  const { isOpen, onOpen, onClose } = useDisclosure()

  const {
    data: subscription,
    isLoading: subscriptionLoading,
  } = trpc.subscription.getCurrentSubscription.useQuery()

  // Check for active cancellation form
  const { data: cancellationForm } = trpc.subscription.getCancellationForm.useQuery()

  // Reset form state when subscription changes or component unmounts
  useEffect(() => {
    if (subscription?.status === "CANCELED") {
      setShowForm(false)
      setFormSubmissionId(null)
      setIsLoading(false)
    }
  }, [subscription?.status])

  const utils = trpc.useUtils()

  const cancelSubscriptionMutation = trpc.subscription.cancelMySubscription.useMutation({
    onSuccess: (result) => {
      toast.success(result.message || "Abonnement annulé avec succès")
      // Invalidate all subscription-related queries to ensure fresh data
      utils.subscription.getCurrentSubscription.invalidate()
      utils.subscription.invalidate()
      setShowForm(false)
      setFormSubmissionId(null)
      onClose()
    },
    onError: (error) => {
      // Check if error requires form completion (fallback)
      if (error.data && 'cause' in error.data && (error.data.cause as { requiresForm?: boolean })?.requiresForm) {
        setShowForm(true)
        return
      }

      // Handle specific error cases
      if (error.message?.includes("déjà annulé") || error.message?.includes("already cancelled")) {
        toast.error("Cet abonnement est déjà annulé")
        // Refresh data to show current state
        utils.subscription.getCurrentSubscription.invalidate()
        utils.subscription.invalidate()
        onClose()
      } else {
        toast.error(error.message || "Erreur lors de l'annulation de l'abonnement")
      }
    },
    onSettled: () => {
      setIsLoading(false)
    },
  })

  const handleCancelSubscription = async () => {
    if (!subscription) return

    // Check if subscription is already cancelled
    if (subscription.status === "CANCELED") {
      toast.error("Cet abonnement est déjà annulé")
      onClose()
      return
    }

    // Check if there's an active cancellation form and no submission yet
    if (cancellationForm && !formSubmissionId) {
      setShowForm(true)
      return
    }

    // Proceed with cancellation (either no form required or form already submitted)
    setIsLoading(true)
    try {
      await cancelSubscriptionMutation.mutateAsync({
        subscriptionId: subscription.id,
        formSubmissionId: formSubmissionId || undefined,
      })
    } catch (error) {
      // Error is handled by the mutation's onError callback
    }
  }

  const handleFormSubmissionComplete = (submissionId: string) => {
    setFormSubmissionId(submissionId)
    setShowForm(false)
    // The backend automatically cancels the subscription when a SUBSCRIPTION_CANCELLATION form is submitted
    // So we just need to refresh the data and close modals
    toast.success("Abonnement annulé avec succès")
    // Invalidate all subscription-related queries to ensure fresh data
    utils.subscription.getCurrentSubscription.invalidate()
    utils.subscription.invalidate()
    onClose() // Close the confirmation modal
  }

  const handleFormCancel = () => {
    setShowForm(false)
    setFormSubmissionId(null) // Reset form submission state
    onClose() // Close the confirmation modal
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "ACTIVE":
        return "success"
      case "PENDING":
        return "warning"
      case "CANCELED":
        return "danger"
      case "EXPIRED":
        return "default"
      case "FAILED":
        return "danger"
      default:
        return "default"
    }
  }

  const getStatusLabel = (status: string) => {
    switch (status) {
      case "ACTIVE":
        return "Actif"
      case "PENDING":
        return "En attente"
      case "CANCELED":
        return "Annulé"
      case "EXPIRED":
        return "Expiré"
      case "FAILED":
        return "Échec"
      default:
        return status
    }
  }

  const getBillingPeriodLabel = (period: string) => {
    return period === "MONTHLY" ? "Mensuel" : "Annuel"
  }

  if (subscriptionLoading) {
    return (
      <Card className={className}>
        <CardBody className="flex items-center justify-center py-8">
          <Spinner size="lg" />
          <p className="mt-4 text-sm text-muted-foreground">Chargement de votre abonnement...</p>
        </CardBody>
      </Card>
    )
  }

  if (!subscription) {
    return (
      <Card className={className}>
        <CardHeader>
          <div className="flex items-center gap-2">
            <Package className="size-5" />
            <h3 className="text-lg font-semibold">Abonnement</h3>
          </div>
        </CardHeader>
        <CardBody>
          <p className="text-sm text-muted-foreground">Vous n&apos;avez pas d&apos;abonnement actif.</p>
        </CardBody>
      </Card>
    )
  }

  return (
    <>
      <Card className={className}>
        <CardHeader>
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center gap-2">
              <Package className="size-5" />
              <h3 className="text-lg font-semibold">Mon Abonnement</h3>
            </div>
            <Chip color={getStatusColor(subscription.status)} variant="flat" size="sm">
              {getStatusLabel(subscription.status)}
            </Chip>
          </div>
        </CardHeader>
        <CardBody className="space-y-4">
          {/* Plan Details */}
          <div>
            <h4 className="font-medium text-sm text-muted-foreground mb-2">Plan actuel</h4>
            <div className="flex items-center justify-between">
              <div>
                <p className="font-semibold">{subscription.plan.name}</p>
                {subscription.plan.description && (
                  <p className="text-sm text-muted-foreground">{subscription.plan.description}</p>
                )}
              </div>
              <div className="text-right">
                <p className="font-semibold">
                  {subscription.billingPeriod === "MONTHLY"
                    ? `${subscription.plan.monthlyPrice / 100}€`
                    : `${subscription.plan.annualPrice / 100}€`}
                </p>
                <p className="text-sm text-muted-foreground">
                  {getBillingPeriodLabel(subscription.billingPeriod)}
                </p>
              </div>
            </div>
          </div>

          <Divider />

          {/* Subscription Details */}
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <Calendar className="size-4 text-muted-foreground" />
              <span className="text-sm">
                <span className="text-muted-foreground">Début:</span>{" "}
                {format(new Date(subscription.startDate), "dd MMMM yyyy", { locale: fr })}
              </span>
            </div>
            <div className="flex items-center gap-2">
              <Calendar className="size-4 text-muted-foreground" />
              <span className="text-sm">
                <span className="text-muted-foreground">Fin:</span>{" "}
                {format(new Date(subscription.endDate), "dd MMMM yyyy", { locale: fr })}
              </span>
            </div>
            {subscription.canceledAt && (
              <div className="flex items-center gap-2">
                <X className="size-4 text-danger" />
                <span className="text-sm">
                  <span className="text-muted-foreground">Annulé le:</span>{" "}
                  {format(new Date(subscription.canceledAt), "dd MMMM yyyy", { locale: fr })}
                </span>
              </div>
            )}
          </div>

          {/* Refund Information */}
          {(() => {
            const refundPercentage = subscription.billingPeriod === "MONTHLY"
              ? subscription.plan.monthlyRefundPercentage
              : subscription.plan.annualRefundPercentage

            return refundPercentage && refundPercentage > 0 ? (
              <>
                <Divider />
                <div className="bg-primary-50 dark:bg-primary-950/20 p-3 rounded-lg">
                  <p className="text-sm text-primary-700 dark:text-primary-300">
                    <strong>Remboursement disponible:</strong> {refundPercentage}% du montant net reçu en cas d&apos;annulation
                  </p>
                  <p className="text-xs text-primary-600 dark:text-primary-400 mt-1">
                    Le remboursement est calculé sur le montant effectivement reçu après déduction des frais de traitement de notre partenaire de paiement MangoPay.
                  </p>
                </div>
              </>
            ) : null
          })()}

          {/* Recent Payments */}
          {subscription.payments && subscription.payments.length > 0 && (
            <>
              <Divider />
              <div>
                <h4 className="font-medium text-sm text-muted-foreground mb-2">Derniers paiements</h4>
                <div className="space-y-2">
                  {subscription.payments.slice(0, 3).map((payment) => (
                    <div key={payment.id} className="flex items-center justify-between text-sm">
                      <div className="flex items-center gap-2">
                        <CreditCard className="size-3 text-muted-foreground" />
                        <span>{format(new Date(payment.createdAt), "dd/MM/yyyy", { locale: fr })}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <span>{payment.amount}€</span>
                        <Chip
                          color={payment.status === "SUCCEEDED" ? "success" : payment.status === "FAILED" ? "danger" : "warning"}
                          variant="flat"
                          size="sm"
                        >
                          {payment.status === "SUCCEEDED" ? "Réussi" : payment.status === "FAILED" ? "Échec" : "En attente"}
                        </Chip>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </>
          )}

          {/* Cancel Button */}
          {subscription.status === "ACTIVE" && (
            <>
              <Divider />
              <div className="pt-2">
                <Button
                  color="danger"
                  variant="light"
                  onPress={onOpen}
                  className="w-full"
                  startContent={<X className="size-4" />}
                >
                  Annuler mon abonnement
                </Button>
              </div>
            </>
          )}
        </CardBody>
      </Card>

      {/* Cancellation Confirmation Modal */}
      <Modal isOpen={isOpen} onClose={onClose} size="md">
        <ModalContent>
          <ModalHeader className="flex flex-col gap-1">
            <div className="flex items-center gap-2">
              <AlertTriangle className="size-5 text-warning" />
              <span>Confirmer l&apos;annulation</span>
            </div>
          </ModalHeader>
          <ModalBody>
            <div className="space-y-4">
              <p>Êtes-vous sûr de vouloir annuler votre abonnement <strong>{subscription?.plan.name}</strong> ?</p>

              {(() => {
                const refundPercentage = subscription?.billingPeriod === "MONTHLY"
                  ? subscription?.plan.monthlyRefundPercentage
                  : subscription?.plan.annualRefundPercentage

                return refundPercentage && refundPercentage > 0 ? (
                  <div className="bg-success-50 dark:bg-success-950/20 p-3 rounded-lg">
                    <p className="text-sm text-success-700 dark:text-success-300">
                      <strong>Bonne nouvelle !</strong> Vous recevrez un remboursement de{" "}
                      <strong>{refundPercentage}%</strong> du montant net reçu suite à cette annulation.
                    </p>
                    <p className="text-xs text-success-600 dark:text-success-400 mt-1">
                      Le montant exact sera calculé sur la somme effectivement reçue après déduction des frais MangoPay.
                    </p>
                  </div>
                ) : null
              })()}

              <div className="bg-warning-50 dark:bg-warning-950/20 p-3 rounded-lg">
                <p className="text-sm text-warning-700 dark:text-warning-300">
                  <strong>Attention :</strong> Cette action est irréversible. Votre accès aux fonctionnalités spéciales de votre plan actuel
                  sera suspendu immédiatement.
                </p>
              </div>
            </div>
          </ModalBody>
          <ModalFooter>
            <Button variant="light" onPress={onClose} disabled={isLoading}>
              Annuler
            </Button>
            <Button
              color="danger"
              onPress={handleCancelSubscription}
              isLoading={isLoading}
              disabled={isLoading}
            >
              Confirmer l&apos;annulation
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* Form Modal */}
      <Modal isOpen={showForm} onClose={handleFormCancel} size="3xl" scrollBehavior="inside">
        <ModalContent>
          <ModalHeader>
            <div className="flex items-center gap-2">
              <AlertTriangle className="size-5 text-warning" />
              <span>Formulaire d&apos;annulation</span>
            </div>
          </ModalHeader>
          <ModalBody className="p-0">
            <DynamicForm
              formType="SUBSCRIPTION_CANCELLATION"
              subscriptionId={subscription?.id}
              onSubmissionComplete={handleFormSubmissionComplete}
              onCancel={handleFormCancel}
              className="border-none shadow-none"
            />
          </ModalBody>
        </ModalContent>
      </Modal>
    </>
  )
}

export default SubscriptionManagement
