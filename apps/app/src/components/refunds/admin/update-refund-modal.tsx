"use client"

import React, { useEffect, useState } from "react"
import { toast } from "react-toastify"

import { trpc } from "@/lib/trpc/client"
import { Button } from "@nextui-org/button"
import { Textarea } from "@nextui-org/input"
import { <PERSON><PERSON>, <PERSON>dal<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader } from "@nextui-org/modal"
import { Select, SelectItem } from "@nextui-org/select"
import { Prisma, RefundStatus } from "@prisma/client"

type RefundWithUserAndPlan = Prisma.RefundGetPayload<{
  include: {
    user: {
      select: {
        id: true
        name: true
        email: true
      }
    }
    plan: {
      select: {
        id: true
        name: true
      }
    }
    subscription: {
      select: {
        id: true
        status: true
      }
    }
  }
}>

interface UpdateRefundModalProps {
  refund: RefundWithUserAndPlan
  isOpen: boolean
  onClose: () => void
  onRefundUpdated: () => void
}

export const UpdateRefundModal: React.FC<UpdateRefundModalProps> = ({
  refund,
  isOpen,
  onClose,
  onRefundUpdated,
}) => {
  const [status, setStatus] = useState<RefundStatus>(refund.status)
  const [failureReason, setFailureReason] = useState(refund.failureReason || "")

  const updateRefundMutation = trpc.refund.updateStatus.useMutation({
    onSuccess: () => {
      toast.success("Statut du remboursement mis à jour avec succès")
      onRefundUpdated()
      onClose()
    },
    onError: (error) => {
      toast.error(error.message || "Erreur lors de la mise à jour du remboursement")
    },
  })

  useEffect(() => {
    if (isOpen) {
      setStatus(refund.status)
      setFailureReason(refund.failureReason || "")
    }
  }, [isOpen, refund])

  const handleSubmit = () => {
    const updateData: {
      id: string
      status: "PENDING" | "COMPLETED" | "FAILED"
      failureReason?: string
    } = {
      id: refund.id,
      status: status as "PENDING" | "COMPLETED" | "FAILED",
    }

    if (status === RefundStatus.FAILED && failureReason.trim()) {
      updateData.failureReason = failureReason.trim()
    }

    updateRefundMutation.mutate(updateData)
  }

  const getStatusText = (status: RefundStatus) => {
    switch (status) {
      case RefundStatus.PENDING:
        return "En attente"
      case RefundStatus.COMPLETED:
        return "Terminé"
      case RefundStatus.FAILED:
        return "Échoué"
      default:
        return status
    }
  }

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="2xl">
      <ModalContent>
        <ModalHeader>
          <div>
            <h3 className="text-lg font-semibold">Modifier le remboursement</h3>
            <p className="text-sm text-default-500">
              Remboursement #{refund.id.slice(-8)} - {refund.user.name || refund.user.email}
            </p>
          </div>
        </ModalHeader>
        <ModalBody>
          <div className="space-y-4">
            {/* Refund Information */}
            <div className="bg-default-50 p-4 rounded-lg">
              <h4 className="font-medium mb-2">Informations du remboursement</h4>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-default-500">Utilisateur:</span>
                  <p className="font-medium">{refund.user.name || "Nom non défini"}</p>
                  <p className="text-default-500">{refund.user.email}</p>
                </div>
                <div>
                  <span className="text-default-500">Plan:</span>
                  <p className="font-medium">{refund.plan.name}</p>
                </div>
                <div>
                  <span className="text-default-500">Montant:</span>
                  <p className="font-medium">{(Number(refund.amount) / 100).toFixed(2)} €</p>
                </div>
                <div>
                  <span className="text-default-500">Statut actuel:</span>
                  <p className="font-medium">{getStatusText(refund.status)}</p>
                </div>
              </div>
            </div>

            {/* Status Update */}
            <div>
              <Select
                label="Nouveau statut"
                placeholder="Sélectionner un statut"
                selectedKeys={[status]}
                onSelectionChange={(keys) => {
                  const selected = Array.from(keys)[0] as RefundStatus
                  setStatus(selected)
                }}
              >
                <SelectItem key={RefundStatus.PENDING} value={RefundStatus.PENDING}>
                  En attente
                </SelectItem>
                <SelectItem key={RefundStatus.COMPLETED} value={RefundStatus.COMPLETED}>
                  Terminé
                </SelectItem>
                <SelectItem key={RefundStatus.FAILED} value={RefundStatus.FAILED}>
                  Échoué
                </SelectItem>
              </Select>
            </div>



            {/* Failure Reason (for failed status) */}
            {status === RefundStatus.FAILED && (
              <div>
                <Textarea
                  label="Raison de l'échec"
                  placeholder="Expliquer pourquoi le remboursement a échoué"
                  value={failureReason}
                  onValueChange={setFailureReason}
                  minRows={3}
                  isRequired
                />
              </div>
            )}
          </div>
        </ModalBody>
        <ModalFooter>
          <Button
            variant="light"
            onPress={onClose}
            isDisabled={updateRefundMutation.isPending}
          >
            Annuler
          </Button>
          <Button
            color="primary"
            onPress={handleSubmit}
            isLoading={updateRefundMutation.isPending}
            isDisabled={
              updateRefundMutation.isPending ||
              (status === RefundStatus.FAILED && !failureReason.trim())
            }
          >
            Mettre à jour
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  )
}
