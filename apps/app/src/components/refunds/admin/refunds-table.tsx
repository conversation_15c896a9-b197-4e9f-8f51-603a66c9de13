"use client"

import React, { use<PERSON><PERSON>back, useEffect, useState } from "react"
import { format } from "date-fns"
import { fr } from "date-fns/locale"
import { Edit, Search } from "lucide-react"
import { toast } from "react-toastify"

import { useConfirmationDialog } from "@/components/ui/confirmation-dialog"
import { trpc } from "@/lib/trpc/client"
import { Button } from "@nextui-org/button"
import { Card, CardBody, CardHeader } from "@nextui-org/card"
import { Chip } from "@nextui-org/chip"
import { Input } from "@nextui-org/input"
import { Pagination } from "@nextui-org/pagination"
import { Select, SelectItem } from "@nextui-org/select"
import { Spinner } from "@nextui-org/spinner"
import { Table, TableBody, TableCell, TableColumn, TableHeader, TableRow } from "@nextui-org/table"
import { Tooltip } from "@nextui-org/tooltip"
import { Prisma, RefundStatus } from "@prisma/client"

import { UpdateRefundModal } from "./update-refund-modal"

type RefundWithUserAndPlan = Prisma.RefundGetPayload<{
  include: {
    user: {
      select: {
        id: true
        name: true
        email: true
      }
    }
    plan: {
      select: {
        id: true
        name: true
      }
    }
    subscription: {
      select: {
        id: true
        status: true
      }
    }
  }
}>

interface RefundsTableProps {
  initialRefunds: RefundWithUserAndPlan[]
  initialPagination: {
    page: number
    pageSize: number
    totalCount: number
    totalPages: number
  }
}

export const RefundsTable: React.FC<RefundsTableProps> = ({ initialRefunds, initialPagination }) => {
  const [refunds, setRefunds] = useState(initialRefunds)
  const [pagination, setPagination] = useState(initialPagination)
  const [page, setPage] = useState(1)
  const [pageSize, setPageSize] = useState(15)
  const [isLoading, setIsLoading] = useState(false)
  const [selectedRefund, setSelectedRefund] = useState<RefundWithUserAndPlan | null>(null)
  const [isUpdateModalOpen, setIsUpdateModalOpen] = useState(false)
  const [filterValue, setFilterValue] = useState("")
  const [statusFilter, setStatusFilter] = useState<RefundStatus | "">("")

  const { ConfirmationDialog } = useConfirmationDialog()

  const refundsQuery = trpc.refund.getAllForAdmin.useQuery(
    { page, pageSize, status: statusFilter || undefined },
    {
      enabled: true,
      initialData: { data: initialRefunds, pagination: initialPagination }
    }
  )

  // Update local state when query data changes
  useEffect(() => {
    if (refundsQuery.data) {
      setRefunds(refundsQuery.data.data)
      setPagination(refundsQuery.data.pagination)
    }
  }, [refundsQuery.data])

  // Set loading state based on query state
  useEffect(() => {
    setIsLoading(refundsQuery.isFetching)
  }, [refundsQuery.isFetching])

  const handleRefundsMutated = useCallback(async () => {
    await refundsQuery.refetch()
  }, [refundsQuery])

  // Retry refund mutation
  const retryRefundMutation = trpc.refund.retry.useMutation({
    onSuccess: () => {
      toast.success("Remboursement relancé avec succès")
      handleRefundsMutated()
    },
    onError: (error) => {
      toast.error(error.message || "Erreur lors de la relance du remboursement")
    },
  })

  const handlePageChange = (newPage: number) => {
    setPage(newPage)
  }

  const handlePageSizeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newSize = Number(e.target.value)
    setPageSize(newSize)
    setPage(1)
  }

  const handleEditRefund = (refund: RefundWithUserAndPlan) => {
    setSelectedRefund(refund)
    setIsUpdateModalOpen(true)
  }

  const handleRetryRefund = (refundId: string) => {
    retryRefundMutation.mutate(refundId)
  }

  const getStatusColor = (status: RefundStatus) => {
    switch (status) {
      case RefundStatus.PENDING:
        return "warning"
      case RefundStatus.COMPLETED:
        return "success"
      case RefundStatus.FAILED:
        return "danger"
      default:
        return "default"
    }
  }

  const getStatusText = (status: RefundStatus) => {
    switch (status) {
      case RefundStatus.PENDING:
        return "En attente"
      case RefundStatus.COMPLETED:
        return "Terminé"
      case RefundStatus.FAILED:
        return "Échoué"
      default:
        return status
    }
  }

  // Filter refunds based on search
  const filteredRefunds = refunds.filter((refund) => {
    if (!filterValue) return true
    const searchLower = filterValue.toLowerCase()
    return (
      refund.user.name?.toLowerCase().includes(searchLower) ||
      refund.user.email?.toLowerCase().includes(searchLower) ||
      refund.plan.name?.toLowerCase().includes(searchLower) ||
      refund.id.toLowerCase().includes(searchLower)
    )
  })

  return (
    <>
      <Card>
        <CardHeader className="flex justify-between items-center">
          <div>
            <h3 className="text-lg font-semibold">Remboursements</h3>
            <p className="text-sm text-default-500">
              {pagination.totalCount} remboursement{pagination.totalCount > 1 ? "s" : ""} au total
            </p>
          </div>
        </CardHeader>
        <CardBody>
          {/* Filters */}
          <div className="flex gap-4 mb-4">
            <Input
              isClearable
              className="w-full sm:max-w-[44%]"
              placeholder="Rechercher par utilisateur, email, plan..."
              startContent={<Search className="text-default-300" size={18} />}
              value={filterValue}
              onClear={() => setFilterValue("")}
              onValueChange={setFilterValue}
            />
            <Select
              className="w-full sm:max-w-[200px]"
              placeholder="Filtrer par statut"
              selectedKeys={statusFilter ? [statusFilter] : []}
              onSelectionChange={(keys) => {
                const selected = Array.from(keys)[0] as RefundStatus | undefined
                setStatusFilter(selected || "")
              }}
            >
              <SelectItem key={RefundStatus.PENDING} value={RefundStatus.PENDING}>
                En attente
              </SelectItem>
              <SelectItem key={RefundStatus.COMPLETED} value={RefundStatus.COMPLETED}>
                Terminé
              </SelectItem>
              <SelectItem key={RefundStatus.FAILED} value={RefundStatus.FAILED}>
                Échoué
              </SelectItem>
            </Select>
          </div>

          <Table aria-label="Remboursements" bottomContent={
            <div className="flex w-full items-center justify-between">
              <Select
                className="w-28"
                size="sm"
                label="Lignes"
                value={pageSize.toString()}
                onChange={handlePageSizeChange}
              >
                <SelectItem key="10" value="10">
                  10
                </SelectItem>
                <SelectItem key="15" value="15">
                  15
                </SelectItem>
                <SelectItem key="25" value="25">
                  25
                </SelectItem>
                <SelectItem key="50" value="50">
                  50
                </SelectItem>
              </Select>
              <Pagination
                showControls
                showShadow
                color="primary"
                page={page}
                total={pagination.totalPages}
                onChange={handlePageChange}
              />
            </div>
          }>
            <TableHeader>
              <TableColumn>UTILISATEUR</TableColumn>
              <TableColumn>PLAN</TableColumn>
              <TableColumn>MONTANT</TableColumn>
              <TableColumn>STATUT</TableColumn>
              <TableColumn>DATE CRÉATION</TableColumn>
              <TableColumn>DATE TRAITEMENT</TableColumn>
              <TableColumn>ACTIONS</TableColumn>
            </TableHeader>
            <TableBody
              items={filteredRefunds}
              loadingContent={<Spinner label="Chargement..." />}
              loadingState={isLoading ? "loading" : "idle"}
              emptyContent="Aucun remboursement trouvé"
            >
              {(refund) => (
                <TableRow key={refund.id}>
                  <TableCell>
                    <div>
                      <p className="font-medium">{refund.user.name || "Nom non défini"}</p>
                      <p className="text-sm text-default-500">{refund.user.email}</p>
                    </div>
                  </TableCell>
                  <TableCell>
                    <p className="font-medium">{refund.plan.name}</p>
                  </TableCell>
                  <TableCell>
                    <p className="font-medium">{(Number(refund.amount) / 100).toFixed(2)} €</p>
                  </TableCell>
                  <TableCell>
                    <Chip color={getStatusColor(refund.status)} variant="flat" size="sm">
                      {getStatusText(refund.status)}
                    </Chip>
                  </TableCell>
                  <TableCell>
                    <p className="text-sm">
                      {format(new Date(refund.createdAt), "dd/MM/yyyy à HH:mm", { locale: fr })}
                    </p>
                  </TableCell>
                  <TableCell>
                    <p className="text-sm">
                      {refund.processedAt
                        ? format(new Date(refund.processedAt), "dd/MM/yyyy à HH:mm", { locale: fr })
                        : "-"}
                    </p>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Tooltip content="Modifier">
                        <Button
                          isIconOnly
                          size="sm"
                          variant="light"
                          color="primary"
                          onPress={() => handleEditRefund(refund)}
                        >
                          <Edit className="size-4" />
                        </Button>
                      </Tooltip>

                      {/* Retry button for failed refunds with canceled subscriptions */}
                      {refund.status === RefundStatus.FAILED &&
                        refund.subscription?.status === "CANCELED" && (
                          <Tooltip content="Relancer le remboursement">
                            <Button
                              isIconOnly
                              size="sm"
                              variant="light"
                              color="warning"
                              onPress={() => handleRetryRefund(refund.id)}
                              isLoading={retryRefundMutation.isPending}
                            >
                              <span className="text-sm">↻</span>
                            </Button>
                          </Tooltip>
                        )}
                    </div>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </CardBody>
      </Card>

      {/* Update Refund Modal */}
      {selectedRefund && (
        <UpdateRefundModal
          refund={selectedRefund}
          isOpen={isUpdateModalOpen}
          onClose={() => {
            setIsUpdateModalOpen(false)
            setSelectedRefund(null)
          }}
          onRefundUpdated={handleRefundsMutated}
        />
      )}

      <ConfirmationDialog />
    </>
  )
}
