"use client"

import React from "react"
import { AlertT<PERSON>gle, CheckCircle, Info, XCircle } from "lucide-react"

import { <PERSON><PERSON> } from "@nextui-org/button"
import { <PERSON>dal, ModalBody, ModalContent, Modal<PERSON>ooter, ModalHeader } from "@nextui-org/modal"

interface ConfirmationDialogProps {
  isOpen: boolean
  onClose: () => void
  onConfirm: () => void
  title: string
  message: string
  confirmText?: string
  cancelText?: string
  variant?: "danger" | "warning" | "success" | "info"
  isLoading?: boolean
}

export const ConfirmationDialog: React.FC<ConfirmationDialogProps> = ({
  isOpen,
  onClose,
  onConfirm,
  title,
  message,
  confirmText = "Confirmer",
  cancelText = "Annuler",
  variant = "warning",
  isLoading = false,
}) => {
  const getIcon = () => {
    switch (variant) {
      case "danger":
        return <XCircle className="size-6 text-danger" />
      case "warning":
        return <AlertTriangle className="size-6 text-warning" />
      case "success":
        return <CheckCircle className="size-6 text-success" />
      case "info":
        return <Info className="size-6 text-primary" />
      default:
        return <AlertTriangle className="size-6 text-warning" />
    }
  }

  const getConfirmButtonColor = () => {
    switch (variant) {
      case "danger":
        return "danger"
      case "warning":
        return "warning"
      case "success":
        return "success"
      case "info":
        return "primary"
      default:
        return "warning"
    }
  }

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="md">
      <ModalContent>
        <ModalHeader>
          <div className="flex items-center gap-3">
            {getIcon()}
            <span>{title}</span>
          </div>
        </ModalHeader>
        <ModalBody>
          <p className="text-default-600">{message}</p>
        </ModalBody>
        <ModalFooter>
          <Button
            variant="light"
            onPress={onClose}
            isDisabled={isLoading}
          >
            {cancelText}
          </Button>
          <Button
            color={getConfirmButtonColor()}
            onPress={onConfirm}
            isLoading={isLoading}
            isDisabled={isLoading}
          >
            {confirmText}
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  )
}

// Hook for easier usage
export const useConfirmationDialog = () => {
  const [isOpen, setIsOpen] = React.useState(false)
  const [config, setConfig] = React.useState<Omit<ConfirmationDialogProps, 'isOpen' | 'onClose' | 'onConfirm'>>({
    title: "",
    message: "",
  })
  const [onConfirmCallback, setOnConfirmCallback] = React.useState<(() => void) | null>(null)

  const showConfirmation = (
    options: Omit<ConfirmationDialogProps, 'isOpen' | 'onClose' | 'onConfirm'> & {
      onConfirm: () => void
    }
  ) => {
    const { onConfirm, ...rest } = options
    setConfig(rest)
    setOnConfirmCallback(() => onConfirm)
    setIsOpen(true)
  }

  const handleConfirm = () => {
    if (onConfirmCallback) {
      onConfirmCallback()
    }
    setIsOpen(false)
  }

  const handleClose = () => {
    setIsOpen(false)
  }

  const ConfirmationDialogComponent = () => (
    <ConfirmationDialog
      {...config}
      isOpen={isOpen}
      onClose={handleClose}
      onConfirm={handleConfirm}
    />
  )

  return {
    showConfirmation,
    ConfirmationDialog: ConfirmationDialogComponent,
  }
}
