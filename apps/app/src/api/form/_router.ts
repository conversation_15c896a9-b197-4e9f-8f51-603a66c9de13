import { z } from "zod"

import { prisma } from "@/lib/prisma"
import { authenticatedProcedure, modoAuthenticatedProcedure, router } from "@/lib/server/trpc"
import { cancelRegistration } from "@/lib/subscription"
import { evaluateShowConditions, type FormResponseValue, type ShowConditions } from "@/types/conditional-logic"
import { logger } from "@coheadcoaching/lib"
import { FormStatus, FormType, QuestionType } from "@prisma/client"
import type { JsonValue } from "@prisma/client/runtime/library"
import { TRPCError } from "@trpc/server"

// Helper function to convert form responses to conditional logic format
const convertResponsesToLogicFormat = (
  responses: Array<{
    questionId: string
    textValue?: string | null
    numberValue?: number | null
    booleanValue?: boolean | null
    dateValue?: Date | null
    selectedOptions?: string[]
  }>
): Record<string, FormResponseValue> => {
  const converted: Record<string, FormResponseValue> = {}
  responses.forEach((response) => {
    converted[response.questionId] = {
      textValue: response.textValue || undefined,
      numberValue: response.numberValue || undefined,
      booleanValue: response.booleanValue || undefined,
      dateValue: response.dateValue || undefined,
      selectedOptions: response.selectedOptions,
    }
  })
  return converted
}

// Helper function to determine which questions should be visible based on conditional logic
const getVisibleQuestions = (
  questions: Array<{
    id: string
    title: string
    isRequired: boolean
    showConditions: JsonValue
  }>,
  responses: Record<string, FormResponseValue>
): Array<{ id: string; title: string; isRequired: boolean }> => {
  return questions.filter((question) => {
    // If no show conditions, always show the question
    if (!question.showConditions) return true

    try {
      const showConditions =
        typeof question.showConditions === "string"
          ? (JSON.parse(question.showConditions) as ShowConditions)
          : (question.showConditions as ShowConditions)

      return evaluateShowConditions(showConditions, responses)
    } catch (error) {
      console.warn("Error evaluating show conditions for question:", question.id, error)
      return true // Show question if there's an error in conditions
    }
  })
}

// Validation schemas
const questionOptionSchema = z.object({
  id: z.string(),
  label: z.string(),
  value: z.string(),
})

const conditionalLogicRuleSchema = z.object({
  id: z.string(),
  questionId: z.string(),
  operator: z.enum([
    "equals",
    "not_equals",
    "contains",
    "not_contains",
    "greater_than",
    "less_than",
    "greater_than_or_equal",
    "less_than_or_equal",
    "is_empty",
    "is_not_empty",
  ]),
  value: z.union([z.string(), z.number(), z.boolean(), z.array(z.string())]),
})

const conditionalLogicGroupSchema = z.object({
  id: z.string(),
  rules: z.array(conditionalLogicRuleSchema),
  logic: z.enum(["AND", "OR"]),
})

const showConditionsSchema = z.object({
  groups: z.array(conditionalLogicGroupSchema),
  groupLogic: z.enum(["AND", "OR"]),
})

const formQuestionSchema = z.object({
  id: z.string().optional(),
  title: z.string().min(1, "Question title is required"),
  description: z.string().nullable().optional(),
  type: z.nativeEnum(QuestionType),
  order: z.number().int().min(0),
  isRequired: z.boolean().default(false),
  minLength: z.number().int().min(0).nullable().optional(),
  maxLength: z.number().int().min(1).nullable().optional(),
  minValue: z.number().nullable().optional(),
  maxValue: z.number().nullable().optional(),
  options: z.array(questionOptionSchema).nullable().optional(),
  showConditions: showConditionsSchema.nullable().optional(),
})

const createFormSchema = z.object({
  title: z.string().min(1, "Form title is required"),
  description: z.string().optional(),
  type: z.nativeEnum(FormType),
  showProgressBar: z.boolean().default(true),
  allowSaveProgress: z.boolean().default(false),
  requireAuth: z.boolean().default(true),
  enableConditionalLogic: z.boolean().default(false),
  questions: z.array(formQuestionSchema).default([]),
})

const updateFormSchema = createFormSchema.partial().extend({
  id: z.string(),
})

const formQuerySchema = z.object({
  page: z.number().int().min(1).default(1),
  pageSize: z.number().int().min(1).max(100).default(10),
  type: z.nativeEnum(FormType).optional(),
  status: z.nativeEnum(FormStatus).optional(),
  search: z.string().optional(),
})

const statusCountSchema = z.object({
  status: z.nativeEnum(FormStatus).optional(),
})

// Helper function to clean up question data before saving
const cleanQuestionData = (question: z.infer<typeof formQuestionSchema>) => {
  const cleaned = { ...question }

  // Convert null values to undefined for optional fields
  if (cleaned.description === null) cleaned.description = undefined
  if (cleaned.minLength === null) cleaned.minLength = undefined
  if (cleaned.maxLength === null) cleaned.maxLength = undefined
  if (cleaned.minValue === null) cleaned.minValue = undefined
  if (cleaned.maxValue === null) cleaned.maxValue = undefined
  if (cleaned.options === null) cleaned.options = undefined
  if (cleaned.showConditions === null) cleaned.showConditions = undefined

  return cleaned
}

export const formRouter = router({
  countByStatus: modoAuthenticatedProcedure.input(statusCountSchema).query(async ({ input }) => {
    const status = input.status
    const total = await prisma.form.count({
      where: { status },
    })
    return total
  }),

  // Get all forms with pagination and filtering
  getAll: modoAuthenticatedProcedure.input(formQuerySchema).query(async ({ input }) => {
    const { page, pageSize, type, status, search } = input
    const skip = (page - 1) * pageSize

    const where = {
      // Exclude archived forms by default unless specifically requested
      status: status ? { equals: status } : { not: FormStatus.ARCHIVED },
      ...(type && { type }),
      ...(search && {
        OR: [
          { title: { contains: search, mode: "insensitive" as const } },
          { description: { contains: search, mode: "insensitive" as const } },
        ],
      }),
    }

    const [forms, total] = await Promise.all([
      prisma.form.findMany({
        where,
        include: {
          creator: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          questions: {
            orderBy: { order: "asc" },
            select: {
              id: true,
              title: true,
              type: true,
              order: true,
              isRequired: true,
            },
          },
          _count: {
            select: {
              submissions: true,
            },
          },
        },
        orderBy: { createdAt: "desc" },
        skip,
        take: pageSize,
      }),
      prisma.form.count({ where }),
    ])

    return {
      data: forms,
      pagination: {
        page,
        pageSize,
        total,
        totalPages: Math.ceil(total / pageSize),
      },
    }
  }),

  // Get archived forms with pagination and filtering
  getArchived: modoAuthenticatedProcedure.input(formQuerySchema).query(async ({ input }) => {
    const { page, pageSize, type, search } = input
    const skip = (page - 1) * pageSize

    const where = {
      status: FormStatus.ARCHIVED,
      ...(type && { type }),
      ...(search && {
        OR: [
          { title: { contains: search, mode: "insensitive" as const } },
          { description: { contains: search, mode: "insensitive" as const } },
        ],
      }),
    }

    const forms = await prisma.form.findMany({
      where,
      skip,
      take: pageSize,
      include: {
        creator: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        questions: {
          select: {
            id: true,
            title: true,
            description: true,
            type: true,
            order: true,
            isRequired: true,
          },
          orderBy: {
            order: "asc",
          },
        },
        _count: {
          select: {
            submissions: true,
          },
        },
      },
      orderBy: {
        updatedAt: "desc",
      },
    })

    const totalCount = await prisma.form.count({ where })

    return {
      data: forms,
      pagination: {
        page,
        pageSize,
        total: totalCount,
        totalPages: Math.ceil(totalCount / pageSize),
      },
    }
  }),

  // Get form by ID with full details
  getById: modoAuthenticatedProcedure.input(z.string()).query(async ({ input: id }) => {
    const form = await prisma.form.findUnique({
      where: { id },
      include: {
        creator: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        questions: {
          orderBy: { order: "asc" },
        },
        _count: {
          select: {
            submissions: true,
          },
        },
      },
    })

    if (!form) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "Form not found",
      })
    }

    return form
  }),

  // Get active form by type (for public use)
  getActiveByType: authenticatedProcedure.input(z.nativeEnum(FormType)).query(async ({ input: type }) => {
    const form = await prisma.form.findFirst({
      where: {
        type,
        status: FormStatus.ACTIVE,
        isActive: true,
      },
      include: {
        questions: {
          orderBy: { order: "asc" },
          select: {
            id: true,
            title: true,
            description: true,
            type: true,
            order: true,
            isRequired: true,
            minLength: true,
            maxLength: true,
            minValue: true,
            maxValue: true,
            options: true,
            showConditions: true,
          },
        },
      },
    })

    return form
  }),

  // Create new form
  create: modoAuthenticatedProcedure.input(createFormSchema).mutation(async ({ ctx, input }) => {
    const userId = ctx.session?.user.id

    if (!userId) {
      throw new TRPCError({
        code: "UNAUTHORIZED",
        message: "User not authenticated",
      })
    }

    const { questions, ...formData } = input

    // Enhanced validation
    if (!formData.title.trim()) {
      throw new TRPCError({
        code: "BAD_REQUEST",
        message: "Form title is required",
      })
    }

    // Validate questions
    for (const question of questions) {
      if (!question.title.trim()) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "All questions must have a title",
        })
      }

      // Validate choice questions have options
      if (question.type === "SINGLE_CHOICE" || question.type === "MULTIPLE_CHOICE") {
        if (!question.options || question.options.length < 2) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: `Question "${question.title}" must have at least 2 options`,
          })
        }

        // Validate option labels
        for (const option of question.options) {
          if (!option.label.trim()) {
            throw new TRPCError({
              code: "BAD_REQUEST",
              message: `All options in question "${question.title}" must have labels`,
            })
          }
        }
      }

      // Validate number/rating ranges
      if (question.type === "NUMBER" || question.type === "RATING") {
        if (
          question.minValue !== undefined &&
          question.minValue !== null &&
          question.maxValue !== undefined &&
          question.maxValue !== null
        ) {
          if (question.minValue >= question.maxValue) {
            throw new TRPCError({
              code: "BAD_REQUEST",
              message: `Question "${question.title}": minimum value must be less than maximum value`,
            })
          }
        }
      }

      // Validate text length constraints
      if (question.type === "TEXT_SHORT" || question.type === "TEXT_LONG") {
        if (
          question.minLength !== undefined &&
          question.minLength !== null &&
          question.maxLength !== undefined &&
          question.maxLength !== null
        ) {
          if (question.minLength >= question.maxLength) {
            throw new TRPCError({
              code: "BAD_REQUEST",
              message: `Question "${question.title}": minimum length must be less than maximum length`,
            })
          }
        }
      }
    }

    return await prisma.$transaction(async (tx) => {
      // Create the form
      const form = await tx.form.create({
        data: {
          ...formData,
          createdBy: userId,
        },
      })

      // Create questions if provided
      if (questions.length > 0) {
        await tx.formQuestion.createMany({
          data: questions.map((question) => {
            const cleanedQuestion = cleanQuestionData(question)
            return {
              ...cleanedQuestion,
              formId: form.id,
              options: cleanedQuestion.options ? JSON.stringify(cleanedQuestion.options) : undefined,
              showConditions: cleanedQuestion.showConditions
                ? JSON.stringify(cleanedQuestion.showConditions)
                : undefined,
            }
          }),
        })
      }

      return form
    })
  }),

  // Update form
  update: modoAuthenticatedProcedure.input(updateFormSchema).mutation(async ({ input }) => {
    const { id, questions, ...formData } = input

    const existingForm = await prisma.form.findUnique({
      where: { id },
      include: { questions: true },
    })

    if (!existingForm) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "Form not found",
      })
    }

    // Enhanced validation for updates
    if (formData.title && !formData.title.trim()) {
      throw new TRPCError({
        code: "BAD_REQUEST",
        message: "Form title cannot be empty",
      })
    }

    // Validate questions if provided
    if (questions) {
      for (const question of questions) {
        if (!question.title.trim()) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "All questions must have a title",
          })
        }

        // Validate choice questions have options
        if (question.type === "SINGLE_CHOICE" || question.type === "MULTIPLE_CHOICE") {
          if (!question.options || question.options.length < 2) {
            throw new TRPCError({
              code: "BAD_REQUEST",
              message: `Question "${question.title}" must have at least 2 options`,
            })
          }
        }
      }
    }

    return await prisma.$transaction(async (tx) => {
      // Update form data
      const updatedForm = await tx.form.update({
        where: { id },
        data: formData,
      })

      // Handle questions update if provided
      if (questions) {
        // Delete existing questions
        await tx.formQuestion.deleteMany({
          where: { formId: id },
        })

        // Create new questions
        if (questions.length > 0) {
          await tx.formQuestion.createMany({
            data: questions.map((question) => {
              const cleanedQuestion = cleanQuestionData(question)
              return {
                ...cleanedQuestion,
                formId: id,
                options: cleanedQuestion.options ? JSON.stringify(cleanedQuestion.options) : undefined,
                showConditions: cleanedQuestion.showConditions
                  ? JSON.stringify(cleanedQuestion.showConditions)
                  : undefined,
              }
            }),
          })
        }
      }

      return updatedForm
    })
  }),

  // Delete form (only if no submissions or if force delete is requested)
  delete: modoAuthenticatedProcedure
    .input(
      z.object({
        id: z.string(),
        force: z.boolean().default(false),
      })
    )
    .mutation(async ({ input }) => {
      const { id, force } = input

      const form = await prisma.form.findUnique({
        where: { id },
        include: {
          _count: {
            select: {
              submissions: true,
            },
          },
        },
      })

      if (!form) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Form not found",
        })
      }

      // Check if form is active - active forms cannot be deleted
      if (form.isActive) {
        throw new TRPCError({
          code: "PRECONDITION_FAILED",
          message: "Cannot delete active form. Deactivate it first.",
        })
      }

      // If form has submissions and force is not true, prevent deletion
      if (form._count.submissions > 0 && !force) {
        throw new TRPCError({
          code: "PRECONDITION_FAILED",
          message: "Form has existing submissions. Use force delete to proceed.",
        })
      }

      // Delete form and all related data in a transaction
      await prisma.$transaction(async (tx) => {
        // Delete all form responses first
        await tx.formResponse.deleteMany({
          where: {
            submission: {
              formId: id,
            },
          },
        })

        // Delete all form submissions
        await tx.formSubmission.deleteMany({
          where: {
            formId: id,
          },
        })

        // Delete all form questions
        await tx.formQuestion.deleteMany({
          where: {
            formId: id,
          },
        })

        // Finally delete the form
        await tx.form.delete({
          where: { id },
        })
      })

      return { success: true }
    }),

  // Activate form (deactivates other forms of same type)
  activate: modoAuthenticatedProcedure.input(z.string()).mutation(async ({ input: id }) => {
    const form = await prisma.form.findUnique({
      where: { id },
      include: {
        questions: true,
      },
    })

    if (!form) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "Form not found",
      })
    }

    // Validate form completeness before activation
    if (form.questions.length === 0) {
      throw new TRPCError({
        code: "PRECONDITION_FAILED",
        message: "Cannot activate form without questions",
      })
    }

    // Validate choice questions have options
    for (const question of form.questions) {
      if (question.type === "SINGLE_CHOICE" || question.type === "MULTIPLE_CHOICE") {
        const options = question.options
          ? (JSON.parse(question.options as string) as Array<{ id: string; label: string; value: string }>)
          : []
        if (options.length < 2) {
          throw new TRPCError({
            code: "PRECONDITION_FAILED",
            message: `Question "${question.title}" must have at least 2 options before activation`,
          })
        }
      }
    }

    // Form validation passed, proceeding with activation

    return await prisma.$transaction(async (tx) => {
      // Deactivate all other forms of the same type
      await tx.form.updateMany({
        where: {
          type: form.type,
          id: { not: id },
        },
        data: {
          isActive: false,
          status: FormStatus.INACTIVE,
        },
      })

      // Activate the target form
      const activatedForm = await tx.form.update({
        where: { id },
        data: {
          isActive: true,
          status: FormStatus.ACTIVE,
        },
      })

      return activatedForm
    })
  }),

  // Deactivate form
  deactivate: modoAuthenticatedProcedure.input(z.string()).mutation(async ({ input: id }) => {
    const form = await prisma.form.update({
      where: { id },
      data: {
        isActive: false,
        status: FormStatus.INACTIVE,
      },
    })

    return form
  }),

  // Archive form
  archive: modoAuthenticatedProcedure.input(z.string()).mutation(async ({ input: id }) => {
    const form = await prisma.form.findUnique({
      where: { id },
    })

    if (!form) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "Form not found",
      })
    }

    const archivedForm = await prisma.form.update({
      where: { id },
      data: {
        isActive: false,
        status: FormStatus.ARCHIVED,
      },
    })

    return archivedForm
  }),

  // Unarchive form (restore to inactive status)
  unarchive: modoAuthenticatedProcedure.input(z.string()).mutation(async ({ input: id }) => {
    const form = await prisma.form.findUnique({
      where: { id },
    })

    if (!form) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "Form not found",
      })
    }

    const unarchivedForm = await prisma.form.update({
      where: { id },
      data: {
        status: FormStatus.INACTIVE,
      },
    })

    return unarchivedForm
  }),

  // Form submission endpoints
  submission: router({
    // Submit form response
    submit: authenticatedProcedure
      .input(
        z.object({
          formId: z.string(),
          subscriptionId: z.string().optional(),
          responses: z.array(
            z.object({
              questionId: z.string(),
              textValue: z.string().optional(),
              numberValue: z.number().optional(),
              booleanValue: z.boolean().optional(),
              dateValue: z.date().optional(),
              selectedOptions: z.array(z.string()).optional(),
            })
          ),
          ipAddress: z.string().optional(),
          userAgent: z.string().optional(),
        })
      )
      .mutation(async ({ ctx, input }) => {
        const userId = ctx.session?.user.id

        if (!userId) {
          throw new TRPCError({
            code: "UNAUTHORIZED",
            message: "User not authenticated",
          })
        }

        const { formId, subscriptionId, responses, ipAddress, userAgent } = input

        // Verify form exists and is active
        const form = await prisma.form.findFirst({
          where: {
            id: formId,
            status: FormStatus.ACTIVE,
            isActive: true,
          },
          include: {
            questions: true,
          },
        })

        if (!form) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Form not found or not active",
          })
        }

        // Convert responses to conditional logic format
        const logicResponses = convertResponsesToLogicFormat(responses)

        // Get visible questions based on conditional logic
        const visibleQuestions = getVisibleQuestions(
          form.questions.map((q) => ({
            id: q.id,
            title: q.title,
            isRequired: q.isRequired,
            showConditions: q.showConditions,
          })),
          logicResponses
        )

        // Validate required questions that are actually visible are answered
        const visibleRequiredQuestions = visibleQuestions.filter((q) => q.isRequired)
        const answeredQuestionIds = responses.map((r) => r.questionId)
        const missingRequired = visibleRequiredQuestions.filter((q) => !answeredQuestionIds.includes(q.id))

        if (missingRequired.length > 0) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: `Missing required questions: ${missingRequired.map((q) => q.title).join(", ")}`,
          })
        }

        const submission = await prisma.$transaction(async (tx) => {
          // Create submission
          const newSubmission = await tx.formSubmission.create({
            data: {
              formId,
              userId,
              subscriptionId,
              isComplete: true,
              submittedAt: new Date(),
              ipAddress,
              userAgent,
            },
          })

          // Create responses
          await tx.formResponse.createMany({
            data: responses.map((response) => ({
              submissionId: newSubmission.id,
              questionId: response.questionId,
              textValue: response.textValue || null,
              numberValue: response.numberValue || null,
              booleanValue: response.booleanValue || null,
              dateValue: response.dateValue || null,
              selectedOptions: response.selectedOptions ? JSON.stringify(response.selectedOptions) : undefined,
            })),
          })

          return newSubmission
        })

        // Automatic subscription cancellation for SUBSCRIPTION_CANCELLATION forms
        if (form.type === FormType.SUBSCRIPTION_CANCELLATION && subscriptionId) {
          try {
            await cancelRegistration(userId, subscriptionId)
          } catch (error) {
            // Log the error but don't fail the form submission
            logger.error("Failed to automatically cancel subscription after form submission:", error)
            // The form submission is still successful, but cancellation failed
            // The user can still manually cancel through the subscription management interface
          }
        }

        return submission
      }),

    // Get submissions for a form (admin only)
    getByForm: modoAuthenticatedProcedure
      .input(
        z.object({
          formId: z.string(),
          page: z.number().int().min(1).default(1),
          pageSize: z.number().int().min(1).max(100).default(10),
        })
      )
      .query(async ({ input }) => {
        const { formId, page, pageSize } = input
        const skip = (page - 1) * pageSize

        const [submissions, total] = await Promise.all([
          prisma.formSubmission.findMany({
            where: { formId },
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                },
              },
              subscription: {
                select: {
                  id: true,
                  status: true,
                  plan: {
                    select: {
                      name: true,
                    },
                  },
                },
              },
              responses: {
                include: {
                  question: {
                    select: {
                      id: true,
                      title: true,
                      type: true,
                    },
                  },
                },
              },
            },
            orderBy: { submittedAt: "desc" },
            skip,
            take: pageSize,
          }),
          prisma.formSubmission.count({ where: { formId } }),
        ])

        return {
          data: submissions,
          pagination: {
            page,
            pageSize,
            total,
            totalPages: Math.ceil(total / pageSize),
          },
        }
      }),

    // Get individual submission by ID
    getById: modoAuthenticatedProcedure.input(z.string()).query(async ({ input: submissionId }) => {
      const submission = await prisma.formSubmission.findUnique({
        where: { id: submissionId },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          subscription: {
            select: {
              id: true,
              status: true,
              plan: {
                select: {
                  name: true,
                },
              },
            },
          },
          responses: {
            include: {
              question: {
                select: {
                  id: true,
                  title: true,
                  type: true,
                  options: true,
                },
              },
            },
            orderBy: {
              question: {
                order: "asc",
              },
            },
          },
          form: {
            select: {
              id: true,
              title: true,
            },
          },
        },
      })

      if (!submission) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Submission not found",
        })
      }

      // Transform responses to include human-readable option labels
      const transformedResponses = submission.responses.map((response) => {
        let transformedSelectedOptions = response.selectedOptions

        // Convert option IDs to labels for choice questions
        if (
          (response.question.type === "SINGLE_CHOICE" || response.question.type === "MULTIPLE_CHOICE") &&
          response.selectedOptions &&
          response.question.options
        ) {
          try {
            const selectedIds = JSON.parse(response.selectedOptions as string) as string[]
            const questionOptions = JSON.parse(response.question.options as string) as Array<{
              id: string
              label: string
              value: string
            }>

            const selectedLabels = selectedIds.map((id) => {
              const option = questionOptions.find((opt) => opt.id === id)
              return option ? option.label : id
            })

            transformedSelectedOptions = JSON.stringify(selectedLabels)
          } catch (error) {
            // If parsing fails, keep original value
            console.warn("Error transforming selected options:", error)
          }
        }

        return {
          ...response,
          selectedOptions: transformedSelectedOptions,
        }
      })

      return {
        ...submission,
        responses: transformedResponses,
      }
    }),

    // Get submission analytics
    getAnalytics: modoAuthenticatedProcedure
      .input(
        z.object({
          formId: z.string(),
          dateFrom: z.date().optional(),
          dateTo: z.date().optional(),
        })
      )
      .query(async ({ input }) => {
        const { formId, dateFrom, dateTo } = input

        const whereClause = {
          formId,
          isComplete: true,
          ...(dateFrom &&
            dateTo && {
              submittedAt: {
                gte: dateFrom,
                lte: dateTo,
              },
            }),
        }

        const [totalSubmissions, form] = await Promise.all([
          prisma.formSubmission.count({ where: whereClause }),
          prisma.form.findUnique({
            where: { id: formId },
            include: {
              questions: {
                orderBy: { order: "asc" },
              },
            },
          }),
        ])

        if (!form) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Form not found",
          })
        }

        // Get response analytics for each question
        const questionAnalytics = await Promise.all(
          form.questions.map(async (question) => {
            const responses = await prisma.formResponse.findMany({
              where: {
                questionId: question.id,
                submission: whereClause,
              },
            })

            const analytics: {
              questionId: string
              questionTitle: string
              questionType: string
              totalResponses: number
              optionCounts?: Record<string, number>
              yesCount?: number
              noCount?: number
              average?: number
              min?: number
              max?: number
              averageLength?: number
            } = {
              questionId: question.id,
              questionTitle: question.title,
              questionType: question.type,
              totalResponses: responses.length,
            }

            // Type-specific analytics
            switch (question.type) {
              case "SINGLE_CHOICE":
              case "MULTIPLE_CHOICE":
                const optionCounts: Record<string, number> = {}
                responses.forEach((response) => {
                  if (response.selectedOptions) {
                    const options = JSON.parse(response.selectedOptions as string) as string[]
                    options.forEach((option: string) => {
                      const optionLabel = question.options
                        ? (
                            JSON.parse(question.options as string) as Array<{
                              id: string
                              label: string
                              value: string
                            }>
                          ).find((o) => o.id === option)?.label
                        : option
                      optionCounts[optionLabel!] = (optionCounts[optionLabel!] || 0) + 1
                    })
                  }
                })
                analytics.optionCounts = optionCounts
                break

              case "YES_NO":
                const yesCount = responses.filter((r) => r.booleanValue === true).length
                const noCount = responses.filter((r) => r.booleanValue === false).length
                analytics.yesCount = yesCount
                analytics.noCount = noCount
                break

              case "RATING":
              case "NUMBER":
                const values = responses.map((r) => r.numberValue).filter((v) => v !== null) as number[]
                if (values.length > 0) {
                  analytics.average = values.reduce((a, b) => a + b, 0) / values.length
                  analytics.min = Math.min(...values)
                  analytics.max = Math.max(...values)
                }
                break

              case "TEXT_SHORT":
              case "TEXT_LONG":
                analytics.averageLength =
                  responses.map((r) => r.textValue?.length || 0).reduce((a, b) => a + b, 0) / responses.length
                break
            }

            return analytics
          })
        )

        return {
          totalSubmissions,
          questionAnalytics,
          form: {
            id: form.id,
            title: form.title,
            type: form.type,
          },
        }
      }),
  }),
})
